// AURACRONCharacter.cpp
// Sistema de Sígilos AURACRON - Implementação da Classe Base do Personagem UE 5.6

#include "Character/AURACRONCharacter.h"
#include "AbilitySystemComponent.h"
#include "GameplayAbilitySpec.h"
#include "GameplayEffect.h"
#include "GameplayTagContainer.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "TimerManager.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"

// Includes para componentes
#include "GAS/AURACRONAttributeSet.h"
#include "Components/AURACRONSigilComponent.h"
#include "Components/AURACRONMovementComponent.h"

// Includes para Enhanced Input System UE 5.6
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"

// Includes para sistema de estados temporais
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"

// Logging
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONCharacter, Log, All);

AAURACRONCharacter::AAURACRONCharacter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
    , TeamID(-1)
    , bAbilitySystemInitialized(false)
    , bTemporalSystemInitialized(false)
    , bSigilFusionSystemInitialized(false)
    , SigilFusionTimer(6.0f * 60.0f) // 6 minutos em segundos
    , SigilFusionCooldown(2.0f * 60.0f) // 2 minutos em segundos
    , bCanReforge(true)
    , LastReforgeTime(0.0f)
    , TemporalStateHistorySize(600) // 10 segundos a 60 FPS
    , CurrentTemporalStateIndex(0)
{
    // Configurações básicas do personagem - otimizado para production
    PrimaryActorTick.bCanEverTick = false; // Desabilitado por padrão, será habilitado apenas quando necessário
    bReplicates = true;
    SetReplicateMovement(true);

    // Configuração da cápsula de colisão
    GetCapsuleComponent()->SetCapsuleHalfHeight(88.0f);
    GetCapsuleComponent()->SetCapsuleRadius(34.0f);
    GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    GetCapsuleComponent()->SetCollisionResponseToAllChannels(ECR_Block);
    GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);

    // Configuração do movimento
    GetCharacterMovement()->bOrientRotationToMovement = true;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
    GetCharacterMovement()->JumpZVelocity = 600.0f;
    GetCharacterMovement()->AirControl = 0.2f;
    GetCharacterMovement()->MaxWalkSpeed = 500.0f;
    GetCharacterMovement()->MinAnalogWalkSpeed = 20.0f;
    GetCharacterMovement()->BrakingDecelerationWalking = 2000.0f;

    // Criar componente do sistema de habilidades
    AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);

    // Criar AttributeSet
    AttributeSet = CreateDefaultSubobject<UAURACRONAttributeSet>(TEXT("AttributeSet"));

    // Criar componente de Sígilos
    SigilComponent = CreateDefaultSubobject<UAURACRONSigilComponent>(TEXT("SigilComponent"));

    // Criar componente de movimento customizado
    AURACRONMovementComponent = CreateDefaultSubobject<UAURACRONMovementComponent>(TEXT("AURACRONMovementComponent"));

    // Inicializar arrays
    EquippedSigils.Empty();
    DefaultAbilities.Empty();
    DefaultEffects.Empty();

    // Inicializar sistema de estados temporais
    TemporalStateHistory.Reserve(TemporalStateHistorySize);

    // Configurar Enhanced Input System
    SetupEnhancedInputSystem();

    // Validação robusta de componentes
    ensure(AbilitySystemComponent != nullptr);
    ensure(AttributeSet != nullptr);
    ensure(SigilComponent != nullptr);
    ensure(AURACRONMovementComponent != nullptr);

    UE_LOG(LogAURACRONCharacter, Log, TEXT("AURACRONCharacter construído com sucesso para %s"), *GetName());
}

void AAURACRONCharacter::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar sistema de habilidades se for o servidor ou cliente local
    if (HasAuthority() || IsLocallyControlled())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogAURACRONCharacter, Log, TEXT("EndPlay iniciado para %s, razão: %d"), *GetName(), static_cast<int32>(EndPlayReason));

    // Cleanup robusto de delegates de atributos
    if (AbilitySystemComponent && bAbilitySystemInitialized)
    {
        if (HealthChangedDelegateHandle.IsValid())
        {
            AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetHealthAttribute()).Remove(HealthChangedDelegateHandle);
            HealthChangedDelegateHandle.Reset();
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Health delegate removido para %s"), *GetName());
        }
        if (ManaChangedDelegateHandle.IsValid())
        {
            AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetManaAttribute()).Remove(ManaChangedDelegateHandle);
            ManaChangedDelegateHandle.Reset();
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Mana delegate removido para %s"), *GetName());
        }
    }

    // Cleanup do sistema temporal
    CleanupTemporalSystem();

    // Cleanup do sistema de fusão
    CleanupSigilFusionSystem();

    // Limpar timers ativos
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
        UE_LOG(LogAURACRONCharacter, Log, TEXT("Timers limpos para %s"), *GetName());
    }

    // Cleanup de componentes VFX
    CleanupVisualEffects();

    // Marcar sistemas como não inicializados
    bAbilitySystemInitialized = false;
    bTemporalSystemInitialized = false;
    bSigilFusionSystemInitialized = false;

    UE_LOG(LogAURACRONCharacter, Log, TEXT("EndPlay concluído para %s"), *GetName());

    Super::EndPlay(EndPlayReason);
}

void AAURACRONCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Lógica de tick personalizada pode ser adicionada aqui
}

void AAURACRONCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);

    // Configurar Enhanced Input System para UE 5.6
    if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent))
    {
        // Validação robusta
        check(EnhancedInputComponent);

        // Configurar ações de movimento
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &AAURACRONCharacter::Move);
        }

        // Configurar ações de câmera
        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &AAURACRONCharacter::Look);
        }

        // Configurar habilidades dos sígilos
        if (SigilAbility1Action)
        {
            EnhancedInputComponent->BindAction(SigilAbility1Action, ETriggerEvent::Started, this, &AAURACRONCharacter::ActivateSigilAbility1);
        }

        if (SigilAbility2Action)
        {
            EnhancedInputComponent->BindAction(SigilAbility2Action, ETriggerEvent::Started, this, &AAURACRONCharacter::ActivateSigilAbility2);
        }

        if (SigilAbility3Action)
        {
            EnhancedInputComponent->BindAction(SigilAbility3Action, ETriggerEvent::Started, this, &AAURACRONCharacter::ActivateSigilAbility3);
        }

        // Configurar fusão de sígilos
        if (SigilFusionAction)
        {
            EnhancedInputComponent->BindAction(SigilFusionAction, ETriggerEvent::Started, this, &AAURACRONCharacter::TriggerSigilFusion);
        }

        // Configurar efeitos temporais
        if (TemporalRewindAction)
        {
            EnhancedInputComponent->BindAction(TemporalRewindAction, ETriggerEvent::Started, this, &AAURACRONCharacter::TriggerTemporalRewind);
        }

        UE_LOG(LogAURACRONCharacter, Log, TEXT("Enhanced Input System configurado com sucesso para %s"), *GetName());
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Error, TEXT("Enhanced Input Component não encontrado para %s"), *GetName());
        ensure(false);
    }
}

void AAURACRONCharacter::PossessedBy(AController* NewController)
{
    Super::PossessedBy(NewController);

    // Inicializar sistema de habilidades no servidor
    if (HasAuthority())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::OnRep_PlayerState()
{
    Super::OnRep_PlayerState();

    // Inicializar sistema de habilidades no cliente
    if (!HasAuthority() && IsLocallyControlled())
    {
        InitializeAbilitySystem();
    }
}

void AAURACRONCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(AAURACRONCharacter, TeamID);
    DOREPLIFETIME(AAURACRONCharacter, EquippedSigils);
}

UAbilitySystemComponent* AAURACRONCharacter::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

void AAURACRONCharacter::InitializeAbilitySystem()
{
    if (bAbilitySystemInitialized)
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("AbilitySystem já inicializado para %s"), *GetName());
        return;
    }

    if (!ensure(AbilitySystemComponent))
    {
        UE_LOG(LogAURACRONCharacter, Error, TEXT("AbilitySystemComponent é nulo para %s"), *GetName());
        return;
    }

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Inicializando AbilitySystem para %s"), *GetName());

    // Inicializar o ASC com validação robusta
    AbilitySystemComponent->InitAbilityActorInfo(this, this);

    // Inicializar o componente de Sígilos com validação
    if (ensure(SigilComponent))
    {
        SigilComponent->InitializeWithAbilitySystem(AbilitySystemComponent);
        UE_LOG(LogAURACRONCharacter, Log, TEXT("SigilComponent inicializado para %s"), *GetName());
    }

    // Conceder habilidades padrão com validação
    GiveDefaultAbilities();

    // Aplicar efeitos padrão com validação
    ApplyDefaultEffects();

    // Aplicar tags iniciais com validação
    if (InitialGameplayTags.Num() > 0)
    {
        AbilitySystemComponent->AddLooseGameplayTags(InitialGameplayTags);
        UE_LOG(LogAURACRONCharacter, Log, TEXT("Tags iniciais aplicadas: %d tags para %s"), InitialGameplayTags.Num(), *GetName());
    }

    // Configurar callbacks de atributos com validação robusta
    if (ensure(AttributeSet))
    {
        HealthChangedDelegateHandle = AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetHealthAttribute()).AddUObject(this, &AAURACRONCharacter::OnHealthAttributeChanged);
        ManaChangedDelegateHandle = AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetManaAttribute()).AddUObject(this, &AAURACRONCharacter::OnManaAttributeChanged);

        if (HealthChangedDelegateHandle.IsValid() && ManaChangedDelegateHandle.IsValid())
        {
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Delegates de atributos configurados com sucesso para %s"), *GetName());
        }
        else
        {
            UE_LOG(LogAURACRONCharacter, Error, TEXT("Falha ao configurar delegates de atributos para %s"), *GetName());
        }
    }

    // Inicializar sistema temporal
    InitializeTemporalSystem();

    // Inicializar sistema de fusão de sígilos
    InitializeSigilFusionSystem();

    bAbilitySystemInitialized = true;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("AbilitySystem inicializado com sucesso para %s"), *GetName());
}

void AAURACRONCharacter::GiveDefaultAbilities()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Conceder habilidades padrão
    for (const TSubclassOf<UGameplayAbility>& AbilityClass : DefaultAbilities)
    {
        if (AbilityClass)
        {
            FGameplayAbilitySpec AbilitySpec(AbilityClass, 1, INDEX_NONE, this);
            AbilitySystemComponent->GiveAbility(AbilitySpec);
        }
    }
}

void AAURACRONCharacter::ApplyDefaultEffects()
{
    if (!HasAuthority() || !AbilitySystemComponent)
    {
        return;
    }

    // Aplicar efeitos padrão
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    for (const TSubclassOf<UGameplayEffect>& EffectClass : DefaultEffects)
    {
        if (EffectClass)
        {
            FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(EffectClass, 1.0f, EffectContext);
            if (EffectSpecHandle.IsValid())
            {
                AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
            }
        }
    }
}

void AAURACRONCharacter::EquipSigil_Implementation(EAURACRONSigilType SigilType)
{
    if (!EquippedSigils.Contains(SigilType))
    {
        EquippedSigils.AddUnique(SigilType);
        OnSigilEquipped(SigilType);
    }
}

void AAURACRONCharacter::UnequipSigil_Implementation(EAURACRONSigilType SigilType)
{
    if (EquippedSigils.Remove(SigilType) > 0)
    {
        OnSigilUnequipped(SigilType);
    }
}

bool AAURACRONCharacter::IsSigilEquipped(EAURACRONSigilType SigilType) const
{
    return EquippedSigils.Contains(SigilType);
}

TArray<EAURACRONSigilType> AAURACRONCharacter::GetEquippedSigils() const
{
    return EquippedSigils;
}

float AAURACRONCharacter::GetHealth() const
{
    return AttributeSet ? AttributeSet->GetHealth() : 0.0f;
}

float AAURACRONCharacter::GetMaxHealth() const
{
    return AttributeSet ? AttributeSet->GetMaxHealth() : 0.0f;
}

float AAURACRONCharacter::GetMana() const
{
    return AttributeSet ? AttributeSet->GetMana() : 0.0f;
}

float AAURACRONCharacter::GetMaxMana() const
{
    return AttributeSet ? AttributeSet->GetMaxMana() : 0.0f;
}

float AAURACRONCharacter::GetAttackDamage() const
{
    return AttributeSet ? AttributeSet->GetAttackDamage() : 0.0f;
}

float AAURACRONCharacter::GetAbilityPower() const
{
    return AttributeSet ? AttributeSet->GetAbilityPower() : 0.0f;
}

void AAURACRONCharacter::ApplyTemporalEffect(EAURACRONTemporalEffectType EffectType, float Duration)
{
    // Validação robusta de parâmetros
    if (Duration <= 0.0f)
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("ApplyTemporalEffect: Duration inválida (%.2f) para %s"), Duration, *GetName());
        return;
    }

    // Implementação robusta de efeitos temporais
    switch (EffectType)
    {
        case EAURACRONTemporalEffectType::Rewind:
        {
            // Sistema completo de rewind temporal
            ExecuteTemporalRewind(Duration);
            break;
        }
        case EAURACRONTemporalEffectType::Slow:
        {
            // Reduzir velocidade de movimento e animações com validação
            if (ensure(AURACRONMovementComponent))
            {
                AURACRONMovementComponent->ApplySpeedModifier(0.5f, Duration, FName("TemporalSlow"));
                SpawnTemporalVFX(EAURACRONTemporalEffectType::Slow, Duration);
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Efeito Temporal Slow aplicado por %.2fs em %s"), Duration, *GetName());
            }
            break;
        }
        case EAURACRONTemporalEffectType::Accelerate:
        {
            // Aumentar velocidade de movimento e animações com validação
            if (ensure(AURACRONMovementComponent))
            {
                AURACRONMovementComponent->ApplySpeedModifier(1.5f, Duration, FName("TemporalAccelerate"));
                SpawnTemporalVFX(EAURACRONTemporalEffectType::Accelerate, Duration);
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Efeito Temporal Accelerate aplicado por %.2fs em %s"), Duration, *GetName());
            }
            break;
        }
        case EAURACRONTemporalEffectType::Freeze:
        {
            // Congelar completamente o personagem com validação
            if (ensure(AURACRONMovementComponent))
            {
                AURACRONMovementComponent->ApplySpeedModifier(0.0f, Duration, FName("TemporalFreeze"));
                SpawnTemporalVFX(EAURACRONTemporalEffectType::Freeze, Duration);
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Efeito Temporal Freeze aplicado por %.2fs em %s"), Duration, *GetName());
            }
            break;
        }
        case EAURACRONTemporalEffectType::Loop:
        {
            // Sistema completo de loop temporal
            ExecuteTemporalLoop(Duration);
            break;
        }
        default:
            UE_LOG(LogAURACRONCharacter, Warning, TEXT("Tipo de efeito temporal não reconhecido: %d"), static_cast<int32>(EffectType));
            break;
    }
}

void AAURACRONCharacter::SetTeamID_Implementation(int32 NewTeamID)
{
    TeamID = NewTeamID;
}

int32 AAURACRONCharacter::GetTeamID() const
{
    return TeamID;
}

bool AAURACRONCharacter::IsAlly(const AAURACRONCharacter* OtherCharacter) const
{
    if (!OtherCharacter)
    {
        return false;
    }

    return TeamID != -1 && TeamID == OtherCharacter->GetTeamID();
}

void AAURACRONCharacter::OnRep_TeamID()
{
    // Lógica robusta quando TeamID é replicado
    UE_LOG(LogAURACRONCharacter, Log, TEXT("TeamID replicado para %s: %d"), *GetName(), TeamID);

    // Atualizar cor do personagem baseada no time
    UpdateTeamVisuals();

    // Notificar componentes sobre mudança de time
    if (SigilComponent)
    {
        // Reequipar sígilos para aplicar efeitos de time se necessário
        TArray<FAURACRONEquippedSigilInfo> CurrentSigils = SigilComponent->GetAllEquippedSigils();
        for (const FAURACRONEquippedSigilInfo& SigilInfo : CurrentSigils)
        {
            // Reaplica efeitos considerando o novo time
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Reaplicando efeitos do sígilo %s para novo time %d"),
                   *UEnum::GetValueAsString(SigilInfo.SigilType), TeamID);
        }
    }

    // Atualizar configurações de colisão baseadas no time
    UpdateTeamCollisionSettings();

    // Chamar evento Blueprint
    OnTeamChanged(TeamID);
}

void AAURACRONCharacter::OnRep_EquippedSigils()
{
    // Lógica robusta quando EquippedSigils são replicados
    UE_LOG(LogAURACRONCharacter, Log, TEXT("EquippedSigils replicados para %s: %d sígilos"), *GetName(), EquippedSigils.Num());

    // Sincronizar com o componente de sígilos
    if (ensure(SigilComponent))
    {
        // Verificar se os sígilos no componente estão sincronizados com a lista replicada
        TArray<FAURACRONEquippedSigilInfo> ComponentSigils = SigilComponent->GetAllEquippedSigils();

        // Se há diferenças, sincronizar
        if (ComponentSigils.Num() != EquippedSigils.Num())
        {
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Sincronizando sígilos: Component=%d, Replicated=%d"),
                   ComponentSigils.Num(), EquippedSigils.Num());

            // Reequipar sígilos baseado na lista replicada
            for (EAURACRONSigilType SigilType : EquippedSigils)
            {
                if (!SigilComponent->IsSigilEquipped(SigilType))
                {
                    SigilComponent->EquipSigil(SigilType, 1, 1); // Valores padrão
                }
            }
        }
    }

    // Atualizar efeitos visuais dos sígilos
    UpdateSigilVisualEffects();

    // Atualizar UI se for o jogador local
    if (IsLocallyControlled())
    {
        UpdateSigilUI();
    }

    // Chamar evento Blueprint
    OnEquippedSigilsChanged(EquippedSigils);
}

void AAURACRONCharacter::OnHealthAttributeChanged(const struct FOnAttributeChangeData& Data)
{
    OnHealthChanged(Data.NewValue, GetMaxHealth());
}

void AAURACRONCharacter::OnManaAttributeChanged(const struct FOnAttributeChangeData& Data)
{
    OnManaChanged(Data.NewValue, GetMaxMana());
}

// ========================================
// IMPLEMENTAÇÕES DE ENHANCED INPUT SYSTEM
// ========================================

void AAURACRONCharacter::SetupEnhancedInputSystem()
{
    // Configurar Enhanced Input Mapping Context
    if (APlayerController* PlayerController = Cast<APlayerController>(GetController()))
    {
        if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
        {
            if (DefaultMappingContext)
            {
                Subsystem->AddMappingContext(DefaultMappingContext, 0);
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Enhanced Input Mapping Context configurado para %s"), *GetName());
            }
            else
            {
                UE_LOG(LogAURACRONCharacter, Warning, TEXT("DefaultMappingContext não definido para %s"), *GetName());
            }
        }
    }
}

void AAURACRONCharacter::Move(const FInputActionValue& Value)
{
    // Validação robusta
    if (!ensure(Controller))
    {
        return;
    }

    // Obter valor do input
    FVector2D MovementVector = Value.Get<FVector2D>();

    // Aplicar movimento com validação
    if (MovementVector.SizeSquared() > 0.0f)
    {
        // Obter direção forward e right
        const FRotator Rotation = Controller->GetControlRotation();
        const FRotator YawRotation(0, Rotation.Yaw, 0);

        const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
        const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

        // Aplicar movimento
        AddMovementInput(ForwardDirection, MovementVector.Y);
        AddMovementInput(RightDirection, MovementVector.X);

        // Log para debug (apenas em desenvolvimento)
        #if !UE_BUILD_SHIPPING
        UE_LOG(LogAURACRONCharacter, VeryVerbose, TEXT("Movimento aplicado: X=%.2f, Y=%.2f"), MovementVector.X, MovementVector.Y);
        #endif
    }
}

void AAURACRONCharacter::Look(const FInputActionValue& Value)
{
    // Validação robusta
    if (!ensure(Controller))
    {
        return;
    }

    // Obter valor do input
    FVector2D LookAxisVector = Value.Get<FVector2D>();

    // Aplicar rotação com validação
    if (LookAxisVector.SizeSquared() > 0.0f)
    {
        AddControllerYawInput(LookAxisVector.X);
        AddControllerPitchInput(LookAxisVector.Y);

        // Log para debug (apenas em desenvolvimento)
        #if !UE_BUILD_SHIPPING
        UE_LOG(LogAURACRONCharacter, VeryVerbose, TEXT("Look aplicado: X=%.2f, Y=%.2f"), LookAxisVector.X, LookAxisVector.Y);
        #endif
    }
}

void AAURACRONCharacter::ActivateSigilAbility1()
{
    // Ativar habilidade específica do Sígilo Aegis - Murallion
    if (ensure(SigilComponent) && IsSigilEquipped(EAURACRONSigilType::Aegis))
    {
        // Usar GameplayAbilitySystem para ativar habilidade específica Murallion
        if (ensure(AbilitySystemComponent))
        {
            // Buscar habilidade Murallion usando a tag correta do sistema existente
            FGameplayTagContainer MurallionTags;
            MurallionTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Aegis.Murallion")));

            bool bSuccess = AbilitySystemComponent->TryActivateAbilitiesByTag(MurallionTags);
            if (bSuccess)
            {
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Murallion (Aegis) ativado por %s - Barreira circular criada"), *GetName());
            }
            else
            {
                UE_LOG(LogAURACRONCharacter, Warning, TEXT("Falha ao ativar Murallion para %s - habilidade não encontrada ou em cooldown"), *GetName());
            }
        }
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Sígilo Aegis não equipado para %s"), *GetName());
    }
}

void AAURACRONCharacter::ActivateSigilAbility2()
{
    // Ativar habilidade específica do Sígilo Ruin - Fracasso Prismal
    if (ensure(SigilComponent) && IsSigilEquipped(EAURACRONSigilType::Ruin))
    {
        // Usar GameplayAbilitySystem para ativar habilidade específica Fracasso Prismal
        if (ensure(AbilitySystemComponent))
        {
            // Buscar habilidade Fracasso Prismal usando a tag correta do sistema existente
            FGameplayTagContainer FracassoTags;
            FracassoTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Ruin.FracassoPrismal")));

            bool bSuccess = AbilitySystemComponent->TryActivateAbilitiesByTag(FracassoTags);
            if (bSuccess)
            {
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Fracasso Prismal (Ruin) ativado por %s - Reset parcial de cooldowns e buff de dano"), *GetName());
            }
            else
            {
                UE_LOG(LogAURACRONCharacter, Warning, TEXT("Falha ao ativar Fracasso Prismal para %s - habilidade não encontrada ou em cooldown"), *GetName());
            }
        }
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Sígilo Ruin não equipado para %s"), *GetName());
    }
}

void AAURACRONCharacter::ActivateSigilAbility3()
{
    // Ativar habilidade específica do Sígilo Vesper - Sopro de Fluxo
    if (ensure(SigilComponent) && IsSigilEquipped(EAURACRONSigilType::Vesper))
    {
        // Usar GameplayAbilitySystem para ativar habilidade específica Sopro de Fluxo
        if (ensure(AbilitySystemComponent))
        {
            // Buscar habilidade Sopro de Fluxo usando a tag correta do sistema existente
            FGameplayTagContainer SoproTags;
            SoproTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Ability.Sigil.Vesper.SoproDeFluxo")));

            bool bSuccess = AbilitySystemComponent->TryActivateAbilitiesByTag(SoproTags);
            if (bSuccess)
            {
                UE_LOG(LogAURACRONCharacter, Log, TEXT("Sopro de Fluxo (Vesper) ativado por %s - Dash para aliado + escudo"), *GetName());
            }
            else
            {
                UE_LOG(LogAURACRONCharacter, Warning, TEXT("Falha ao ativar Sopro de Fluxo para %s - habilidade não encontrada ou em cooldown"), *GetName());
            }
        }
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Sígilo Vesper não equipado para %s"), *GetName());
    }
}

void AAURACRONCharacter::TriggerSigilFusion()
{
    // Ativar fusão de sígilos com validação robusta
    if (!ensure(SigilComponent))
    {
        return;
    }

    if (CanTriggerSigilFusion())
    {
        // Ativar fusão usando o método do componente
        SigilComponent->ActivateSigilFusion();

        UE_LOG(LogAURACRONCharacter, Log, TEXT("Fusão de Sígilos ativada por %s"), *GetName());
        // Spawnar VFX de fusão
        SpawnSigilFusionVFX();
        // Iniciar cooldown
        LastReforgeTime = GetWorld()->GetTimeSeconds();
        bCanReforge = false;

        // Configurar timer para reabilitar re-forjamento
        FTimerHandle ReforgeTimer;
        GetWorld()->GetTimerManager().SetTimer(ReforgeTimer, this, &AAURACRONCharacter::EnableReforge, SigilFusionCooldown, false);
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Fusão de Sígilos não disponível para %s"), *GetName());
    }
}

void AAURACRONCharacter::TriggerTemporalRewind()
{
    // Ativar rewind temporal com validação
    if (CanUseTemporalAbilities())
    {
        ApplyTemporalEffect(EAURACRONTemporalEffectType::Rewind, 2.0f);
        UE_LOG(LogAURACRONCharacter, Log, TEXT("Rewind Temporal ativado por %s"), *GetName());
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Rewind Temporal não disponível para %s"), *GetName());
    }
}

// ========================================
// SISTEMA DE ESTADOS TEMPORAIS COMPLETO
// ========================================

void AAURACRONCharacter::InitializeTemporalSystem()
{
    if (bTemporalSystemInitialized)
    {
        return;
    }

    // Inicializar histórico de estados temporais
    TemporalStateHistory.Empty();
    TemporalStateHistory.Reserve(TemporalStateHistorySize);
    CurrentTemporalStateIndex = 0;

    // Habilitar tick para captura de estados
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 1.0f / 60.0f; // 60 FPS para captura precisa

    bTemporalSystemInitialized = true;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("Sistema Temporal inicializado para %s"), *GetName());
}

void AAURACRONCharacter::CleanupTemporalSystem()
{
    if (!bTemporalSystemInitialized)
    {
        return;
    }

    // Limpar histórico de estados
    TemporalStateHistory.Empty();

    // Desabilitar tick se não for necessário
    if (!bSigilFusionSystemInitialized)
    {
        PrimaryActorTick.bCanEverTick = false;
    }

    bTemporalSystemInitialized = false;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("Sistema Temporal limpo para %s"), *GetName());
}

void AAURACRONCharacter::CaptureTemporalState()
{
    if (!bTemporalSystemInitialized || !ensure(AttributeSet))
    {
        return;
    }

    // Criar novo estado temporal
    FAURACRONTemporalState NewState;
    NewState.Timestamp = GetWorld()->GetTimeSeconds();
    NewState.Location = GetActorLocation();
    NewState.Rotation = GetActorRotation();
    NewState.Velocity = GetVelocity();
    NewState.Health = AttributeSet->GetHealth();
    NewState.Mana = AttributeSet->GetMana();

    // Capturar estado das habilidades
    if (AbilitySystemComponent)
    {
        // Capturar cooldowns ativos
        TArray<FGameplayAbilitySpec> AbilitySpecs = AbilitySystemComponent->GetActivatableAbilities();
        for (const FGameplayAbilitySpec& Spec : AbilitySpecs)
        {
            if (Spec.Ability)
            {
                // UE 5.6 Modern API - Usar GetCooldownTimeRemaining da própria habilidade
                float CooldownRemaining = 0.0f;
                float CooldownDuration = 0.0f;
                Spec.Ability->GetCooldownTimeRemainingAndDuration(Spec.Handle, AbilitySystemComponent->AbilityActorInfo.Get(), CooldownRemaining, CooldownDuration);

                if (CooldownRemaining > 0.0f)
                {
                    NewState.AbilityCooldowns.Add(Spec.Handle, CooldownRemaining);
                }
            }
        }

        // Capturar tags ativas
        AbilitySystemComponent->GetOwnedGameplayTags(NewState.ActiveGameplayTags);
    }

    // Adicionar ao histórico circular
    if (TemporalStateHistory.Num() >= TemporalStateHistorySize)
    {
        TemporalStateHistory[CurrentTemporalStateIndex] = NewState;
        CurrentTemporalStateIndex = (CurrentTemporalStateIndex + 1) % TemporalStateHistorySize;
    }
    else
    {
        TemporalStateHistory.Add(NewState);
        CurrentTemporalStateIndex = TemporalStateHistory.Num() - 1;
    }
}

void AAURACRONCharacter::ExecuteTemporalRewind(float Duration)
{
    if (!bTemporalSystemInitialized || TemporalStateHistory.Num() == 0)
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Sistema Temporal não inicializado ou sem histórico para %s"), *GetName());
        return;
    }

    // Calcular quantos estados voltar (aproximadamente 3 segundos)
    int32 StatesToRewind = FMath::Min(180, TemporalStateHistory.Num() - 1); // 3 segundos a 60 FPS
    int32 TargetIndex = (CurrentTemporalStateIndex - StatesToRewind + TemporalStateHistorySize) % TemporalStateHistorySize;

    if (TargetIndex >= 0 && TargetIndex < TemporalStateHistory.Num())
    {
        const FAURACRONTemporalState& TargetState = TemporalStateHistory[TargetIndex];

        // Aplicar estado temporal
        RestoreTemporalState(TargetState);

        // Spawnar VFX de rewind
        SpawnTemporalVFX(EAURACRONTemporalEffectType::Rewind, Duration);

        UE_LOG(LogAURACRONCharacter, Log, TEXT("Rewind executado para %s: voltou %d estados"), *GetName(), StatesToRewind);
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Error, TEXT("Índice de rewind inválido para %s: %d"), *GetName(), TargetIndex);
    }
}

void AAURACRONCharacter::RestoreTemporalState(const FAURACRONTemporalState& State)
{
    if (!ensure(AttributeSet) || !ensure(AbilitySystemComponent))
    {
        return;
    }

    // Restaurar posição e rotação
    SetActorLocationAndRotation(State.Location, State.Rotation);

    // Restaurar velocidade
    if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
    {
        MovementComp->Velocity = State.Velocity;
    }

    // Restaurar atributos usando GameplayEffects
    if (TemporalRestoreEffect)
    {
        FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(TemporalRestoreEffect, 1.0f, EffectContext);
        if (EffectSpecHandle.IsValid())
        {
            // Configurar valores de restauração
            EffectSpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Health")), State.Health);
            EffectSpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Mana")), State.Mana);

            AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
        }
    }

    // Restaurar cooldowns das habilidades
    for (const auto& CooldownPair : State.AbilityCooldowns)
    {
        // Aplicar cooldown usando GameplayEffect
        if (CooldownEffect)
        {
            FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            FGameplayEffectSpecHandle EffectSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(CooldownEffect, 1.0f, EffectContext);
            if (EffectSpecHandle.IsValid())
            {
                EffectSpecHandle.Data->SetDuration(CooldownPair.Value, false);
                AbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
            }
        }
    }

    // Restaurar tags ativas
    FGameplayTagContainer CurrentTags;
    AbilitySystemComponent->GetOwnedGameplayTags(CurrentTags);

    // Remover tags que não estavam ativas
    FGameplayTagContainer TagsToRemove = CurrentTags.Filter(FGameplayTagContainer::CreateFromArray(State.ActiveGameplayTags.GetGameplayTagArray()));
    AbilitySystemComponent->RemoveLooseGameplayTags(TagsToRemove);

    // Adicionar tags que estavam ativas
    AbilitySystemComponent->AddLooseGameplayTags(State.ActiveGameplayTags);

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Estado temporal restaurado para %s"), *GetName());
}

void AAURACRONCharacter::ExecuteTemporalLoop(float Duration)
{
    if (!bTemporalSystemInitialized || TemporalStateHistory.Num() < 60) // Mínimo 1 segundo de histórico
    {
        UE_LOG(LogAURACRONCharacter, Warning, TEXT("Sistema Temporal não tem histórico suficiente para loop em %s"), *GetName());
        return;
    }

    // Capturar estados dos últimos 2 segundos para o loop
    int32 LoopStates = FMath::Min(120, TemporalStateHistory.Num()); // 2 segundos a 60 FPS
    TArray<FAURACRONTemporalState> LoopSequence;

    for (int32 i = 0; i < LoopStates; i++)
    {
        int32 Index = (CurrentTemporalStateIndex - i + TemporalStateHistorySize) % TemporalStateHistorySize;
        if (Index >= 0 && Index < TemporalStateHistory.Num())
        {
            LoopSequence.Add(TemporalStateHistory[Index]);
        }
    }

    // Reverter a sequência para ordem cronológica
    Algo::Reverse(LoopSequence);

    // Iniciar reprodução do loop
    StartTemporalLoop(LoopSequence, Duration);

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Loop temporal iniciado para %s com %d estados"), *GetName(), LoopSequence.Num());
}

void AAURACRONCharacter::StartTemporalLoop(const TArray<FAURACRONTemporalState>& LoopSequence, float Duration)
{
    if (LoopSequence.Num() == 0)
    {
        return;
    }

    // Armazenar sequência do loop
    CurrentLoopSequence = LoopSequence;
    CurrentLoopIndex = 0;
    bIsInTemporalLoop = true;
    LoopEndTime = GetWorld()->GetTimeSeconds() + Duration;

    // Configurar timer para reprodução do loop
    FTimerHandle LoopTimer;
    GetWorld()->GetTimerManager().SetTimer(
        LoopTimer,
        this,
        &AAURACRONCharacter::UpdateTemporalLoop,
        1.0f / 60.0f, // 60 FPS
        true
    );

    // Spawnar VFX de loop
    SpawnTemporalVFX(EAURACRONTemporalEffectType::Loop, Duration);

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Timer de loop temporal configurado para %s"), *GetName());
}

void AAURACRONCharacter::UpdateTemporalLoop()
{
    if (!bIsInTemporalLoop || CurrentLoopSequence.Num() == 0)
    {
        return;
    }

    // Verificar se o loop deve terminar
    if (GetWorld()->GetTimeSeconds() >= LoopEndTime)
    {
        EndTemporalLoop();
        return;
    }

    // Aplicar estado atual do loop
    if (CurrentLoopIndex < CurrentLoopSequence.Num())
    {
        const FAURACRONTemporalState& CurrentState = CurrentLoopSequence[CurrentLoopIndex];

        // Aplicar apenas posição e rotação para o loop (não atributos)
        SetActorLocationAndRotation(CurrentState.Location, CurrentState.Rotation);

        // Aplicar velocidade
        if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
        {
            MovementComp->Velocity = CurrentState.Velocity;
        }

        // Avançar para próximo estado
        CurrentLoopIndex++;

        // Reiniciar loop se chegou ao fim
        if (CurrentLoopIndex >= CurrentLoopSequence.Num())
        {
            CurrentLoopIndex = 0;
        }
    }
}

void AAURACRONCharacter::EndTemporalLoop()
{
    if (!bIsInTemporalLoop)
    {
        return;
    }

    bIsInTemporalLoop = false;
    CurrentLoopSequence.Empty();
    CurrentLoopIndex = 0;

    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(TemporalLoopTimer);
    }

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Loop temporal finalizado para %s"), *GetName());
}

// ========================================
// SISTEMA DE FUSÃO DE SÍGILOS COMPLETO
// ========================================

void AAURACRONCharacter::InitializeSigilFusionSystem()
{
    if (bSigilFusionSystemInitialized)
    {
        return;
    }

    // Configurar timer para fusão automática aos 6 minutos
    if (UWorld* World = GetWorld())
    {
        FTimerHandle AutoFusionTimer;
        World->GetTimerManager().SetTimer(
            AutoFusionTimer,
            this,
            &AAURACRONCharacter::TriggerAutomaticSigilFusion,
            SigilFusionTimer,
            false
        );

        UE_LOG(LogAURACRONCharacter, Log, TEXT("Timer de fusão automática configurado para %s (%.1f segundos)"), *GetName(), SigilFusionTimer);
    }

    bSigilFusionSystemInitialized = true;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("Sistema de Fusão de Sígilos inicializado para %s"), *GetName());
}

void AAURACRONCharacter::CleanupSigilFusionSystem()
{
    if (!bSigilFusionSystemInitialized)
    {
        return;
    }

    // Limpar timers relacionados à fusão
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    bSigilFusionSystemInitialized = false;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("Sistema de Fusão de Sígilos limpo para %s"), *GetName());
}

void AAURACRONCharacter::TriggerAutomaticSigilFusion()
{
    if (!ensure(SigilComponent))
    {
        return;
    }

    // Verificar se há sígilos equipados para fusão
    if (EquippedSigils.Num() > 0)
    {
        // Ativar fusão automática
        SigilComponent->ActivateSigilFusion();

        UE_LOG(LogAURACRONCharacter, Log, TEXT("Fusão automática de sígilos ativada para %s"), *GetName());

        // Spawnar VFX de fusão automática
        SpawnAutomaticFusionVFX();

        // Notificar jogador
        OnAutomaticSigilFusion();
    }
    else
    {
        UE_LOG(LogAURACRONCharacter, Log, TEXT("Nenhum sígilo equipado para fusão automática em %s"), *GetName());
    }
}

bool AAURACRONCharacter::CanTriggerSigilFusion() const
{
    // Verificar se pode fazer re-forjamento
    if (!bCanReforge)
    {
        float TimeSinceLastReforge = GetWorld()->GetTimeSeconds() - LastReforgeTime;
        if (TimeSinceLastReforge < SigilFusionCooldown)
        {
            return false;
        }
    }

    // Verificar se tem sígilos equipados
    if (EquippedSigils.Num() == 0)
    {
        return false;
    }

    // Verificar se o componente de sígilos está disponível
    if (!SigilComponent || !SigilComponent->IsFusionAvailable())
    {
        return false;
    }

    return true;
}

void AAURACRONCharacter::EnableReforge()
{
    bCanReforge = true;
    UE_LOG(LogAURACRONCharacter, Log, TEXT("Re-forjamento habilitado para %s"), *GetName());

    // Notificar UI
    OnReforgeAvailable();
}

bool AAURACRONCharacter::CanUseTemporalAbilities() const
{
    // Verificar se o sistema temporal está inicializado
    if (!bTemporalSystemInitialized)
    {
        return false;
    }

    // Verificar se não está em loop temporal
    if (bIsInTemporalLoop)
    {
        return false;
    }

    // Verificar se tem histórico suficiente
    if (TemporalStateHistory.Num() < 60) // Mínimo 1 segundo
    {
        return false;
    }

    return true;
}

// ========================================
// FUNÇÕES DE VFX E EFEITOS VISUAIS
// ========================================

void AAURACRONCharacter::SpawnTemporalVFX(EAURACRONTemporalEffectType EffectType, float Duration)
{
    if (!GetWorld())
    {
        return;
    }

    UNiagaraSystem* VFXToSpawn = nullptr;

    // Selecionar VFX baseado no tipo de efeito
    switch (EffectType)
    {
        case EAURACRONTemporalEffectType::Rewind:
            VFXToSpawn = TemporalRewindVFX;
            break;
        case EAURACRONTemporalEffectType::Slow:
            VFXToSpawn = TemporalSlowVFX;
            break;
        case EAURACRONTemporalEffectType::Accelerate:
            VFXToSpawn = TemporalAccelerateVFX;
            break;
        case EAURACRONTemporalEffectType::Freeze:
            VFXToSpawn = TemporalFreezeVFX;
            break;
        case EAURACRONTemporalEffectType::Loop:
            VFXToSpawn = TemporalLoopVFX;
            break;
        default:
            break;
    }

    if (VFXToSpawn)
    {
        UNiagaraComponent* SpawnedVFX = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            VFXToSpawn,
            GetActorLocation(),
            GetActorRotation(),
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::None,
            true
        );

        if (SpawnedVFX)
        {
            // Configurar duração do efeito
            SpawnedVFX->SetFloatParameter(FName("Duration"), Duration);

            // Armazenar referência para cleanup
            ActiveTemporalVFX.Add(SpawnedVFX);

            // Configurar timer para remover VFX
            FTimerHandle VFXTimer;
            GetWorld()->GetTimerManager().SetTimer(
                VFXTimer,
                [this, SpawnedVFX]()
                {
                    if (IsValid(SpawnedVFX))
                    {
                        SpawnedVFX->DestroyComponent();
                        ActiveTemporalVFX.Remove(SpawnedVFX);
                    }
                },
                Duration,
                false
            );

            UE_LOG(LogAURACRONCharacter, Log, TEXT("VFX temporal spawned para %s: %s"), *GetName(), *UEnum::GetValueAsString(EffectType));
        }
    }
}

// Função removida - VFX das habilidades são gerenciados pelas próprias classes USigilAbility_*
// As habilidades Murallion, Fracasso Prismal e Sopro de Fluxo já têm seus próprios sistemas VFX integrados

void AAURACRONCharacter::SpawnSigilFusionVFX()
{
    if (!GetWorld() || !SigilFusionVFX)
    {
        return;
    }

    UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        SigilFusionVFX,
        GetActorLocation(),
        GetActorRotation(),
        FVector(1.0f),
        true,
        true,
        ENCPoolMethod::None,
        true
    );

    UE_LOG(LogAURACRONCharacter, Log, TEXT("VFX de fusão de sígilos spawned para %s"), *GetName());
}

void AAURACRONCharacter::SpawnAutomaticFusionVFX()
{
    if (!GetWorld() || !AutomaticFusionVFX)
    {
        return;
    }

    UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        AutomaticFusionVFX,
        GetActorLocation(),
        GetActorRotation(),
        FVector(1.0f),
        true,
        true,
        ENCPoolMethod::None,
        true
    );

    UE_LOG(LogAURACRONCharacter, Log, TEXT("VFX de fusão automática spawned para %s"), *GetName());
}

void AAURACRONCharacter::CleanupVisualEffects()
{
    // Limpar VFX temporais ativos
    for (UNiagaraComponent* VFX : ActiveTemporalVFX)
    {
        if (IsValid(VFX))
        {
            VFX->DestroyComponent();
        }
    }
    ActiveTemporalVFX.Empty();

    UE_LOG(LogAURACRONCharacter, Log, TEXT("Efeitos visuais limpos para %s"), *GetName());
}

// ========================================
// FUNÇÕES AUXILIARES E DE SUPORTE
// ========================================

void AAURACRONCharacter::UpdateTeamVisuals()
{
    // Atualizar cor do personagem baseada no time
    if (GetMesh() && TeamMaterials.Contains(TeamID))
    {
        UMaterialInterface* TeamMaterial = TeamMaterials[TeamID];
        if (TeamMaterial)
        {
            GetMesh()->SetMaterial(0, TeamMaterial);
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Material do time atualizado para %s: Team %d"), *GetName(), TeamID);
        }
    }
}

void AAURACRONCharacter::UpdateTeamCollisionSettings()
{
    // Atualizar configurações de colisão baseadas no time
    if (UCapsuleComponent* CapsuleComp = GetCapsuleComponent())
    {
        // Aliados não colidem entre si
        if (TeamID >= 0)
        {
            CapsuleComp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Ignore);
            UE_LOG(LogAURACRONCharacter, Log, TEXT("Configurações de colisão de time atualizadas para %s"), *GetName());
        }
    }
}

void AAURACRONCharacter::UpdateSigilVisualEffects()
{
    // Atualizar efeitos visuais dos sígilos equipados
    for (EAURACRONSigilType SigilType : EquippedSigils)
    {
        SpawnSigilEquipVFX(SigilType);
    }
}

void AAURACRONCharacter::SpawnSigilEquipVFX(EAURACRONSigilType SigilType)
{
    if (!GetWorld())
    {
        return;
    }

    UNiagaraSystem* VFXToSpawn = nullptr;

    switch (SigilType)
    {
        case EAURACRONSigilType::Aegis:
            VFXToSpawn = AegisEquipVFX;
            break;
        case EAURACRONSigilType::Ruin:
            VFXToSpawn = RuinEquipVFX;
            break;
        case EAURACRONSigilType::Vesper:
            VFXToSpawn = VesperEquipVFX;
            break;
        default:
            break;
    }

    if (VFXToSpawn)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            VFXToSpawn,
            GetActorLocation(),
            GetActorRotation()
        );
    }
}

void AAURACRONCharacter::UpdateSigilUI()
{
    // Notificar UI sobre mudanças nos sígilos
    OnSigilUIUpdateRequired();
}

void AAURACRONCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Capturar estado temporal apenas se o sistema estiver inicializado
    if (bTemporalSystemInitialized)
    {
        CaptureTemporalState();
    }
}

// ========================================
// IMPLEMENTAÇÕES DE REPLICAÇÃO OTIMIZADA
// ========================================

void AAURACRONCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicação otimizada com condições
    DOREPLIFETIME_CONDITION(AAURACRONCharacter, TeamID, COND_InitialOnly);
    DOREPLIFETIME_CONDITION(AAURACRONCharacter, EquippedSigils, COND_OwnerOnly);
}
